<script setup lang="ts">
import { ref } from 'vue';
import { type ECOption, useEcharts } from '@/hooks/common/echarts';

defineOptions({
  name: 'CenterBottomContainer'
});

interface Props {
  title: string;
  icon: string;
  cardType?: string;
  animationDelay?: number;
}

const props = withDefaults(defineProps<Props>(), {
  cardType: 'info',
  animationDelay: 0
});

// 模拟数据
const chartData = ref({
  networkQuality: {
    title: '网络质量',
    data: [85, 78], // 移动、电信
    color: '#1890ff'
  },
  internetQuality: {
    title: '上网质量',
    data: [92, 88], // 移动、电信
    color: '#52c41a'
  },
  maintenanceService: {
    title: '装维服务',
    data: [76, 82], // 移动、电信
    color: '#faad14'
  },
  enterpriseProduct: {
    title: '政企产品质量',
    data: [89, 85], // 移动、电信
    color: '#722ed1'
  }
});

// 创建柱状图配置
const createBarChartOption = (title: string, data: number[], color: string) => ({
  title: {
    text: title,
    left: 'center',
    top: 10,
    textStyle: {
      color: '#ffffff',
      fontSize: 14,
      fontWeight: 600
    }
  },
  tooltip: {
    trigger: 'axis' as const,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderColor: color,
    borderWidth: 1,
    textStyle: {
      color: '#ffffff'
    },
    formatter: (params: any) => {
      const param = params[0];
      return `${param.name}: ${param.value}%`;
    }
  },
  grid: {
    left: '15%',
    right: '15%',
    top: '25%',
    bottom: '20%',
    containLabel: true
  },
  xAxis: {
    type: 'category' as const,
    data: ['移动', '电信'],
    axisLine: {
      lineStyle: {
        color: '#ffffff40'
      }
    },
    axisLabel: {
      color: '#ffffff80',
      fontSize: 12
    },
    axisTick: {
      show: false
    }
  },
  yAxis: {
    type: 'value' as const,
    min: 0,
    max: 100,
    axisLine: {
      show: false
    },
    axisTick: {
      show: false
    },
    axisLabel: {
      color: '#ffffff60',
      fontSize: 10,
      formatter: '{value}%'
    },
    splitLine: {
      lineStyle: {
        color: '#ffffff20',
        type: 'dashed' as const
      }
    }
  },
  series: [
    {
      type: 'bar' as const,
      data,
      barWidth: '50%',
      itemStyle: {
        color: {
          type: 'linear' as const,
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color
            },
            {
              offset: 1,
              color: `${color}60`
            }
          ]
        },
        borderRadius: [4, 4, 0, 0],
        shadowColor: color,
        shadowBlur: 10,
        shadowOffsetY: 2
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 20,
          shadowColor: color
        }
      },
      label: {
        show: true,
        position: 'top' as const,
        color: '#ffffff',
        fontSize: 12,
        fontWeight: 600,
        formatter: '{c}%'
      }
    }
  ]
});

// 初始化四个图表
const { domRef: networkQualityRef } = useEcharts(() =>
  createBarChartOption(
    chartData.value.networkQuality.title,
    chartData.value.networkQuality.data,
    chartData.value.networkQuality.color
  )
);

const { domRef: internetQualityRef } = useEcharts(() =>
  createBarChartOption(
    chartData.value.internetQuality.title,
    chartData.value.internetQuality.data,
    chartData.value.internetQuality.color
  )
);

const { domRef: maintenanceServiceRef } = useEcharts(() =>
  createBarChartOption(
    chartData.value.maintenanceService.title,
    chartData.value.maintenanceService.data,
    chartData.value.maintenanceService.color
  )
);

const { domRef: enterpriseProductRef } = useEcharts(() =>
  createBarChartOption(
    chartData.value.enterpriseProduct.title,
    chartData.value.enterpriseProduct.data,
    chartData.value.enterpriseProduct.color
  )
);
</script>

<template>
  <DashboardCard
    :title="props.title"
    :title-icon="props.icon"
    :card-type="props.cardType as any"
    height="100%"
    :animation-delay="props.animationDelay"
    :show-fullscreen="true"
  >
    <!-- 四个柱状图的网格布局 -->
    <div class="grid grid-cols-2 grid-rows-2 h-full gap-16px p-16px">
      <!-- 网络质量 -->
      <div class="relative border border-white/10 rounded-8px bg-white/5 p-12px backdrop-blur-sm">
        <div ref="networkQualityRef" class="h-full w-full" />
      </div>

      <!-- 上网质量 -->
      <div class="relative border border-white/10 rounded-8px bg-white/5 p-12px backdrop-blur-sm">
        <div ref="internetQualityRef" class="h-full w-full" />
      </div>

      <!-- 装维服务 -->
      <div class="relative border border-white/10 rounded-8px bg-white/5 p-12px backdrop-blur-sm">
        <div ref="maintenanceServiceRef" class="h-full w-full" />
      </div>

      <!-- 政企产品质量 -->
      <div class="relative border border-white/10 rounded-8px bg-white/5 p-12px backdrop-blur-sm">
        <div ref="enterpriseProductRef" class="h-full w-full" />
      </div>
    </div>

    <!-- 全屏内容 -->
    <template #fullscreen-content>
      <div class="h-full p-24px">
        <div class="mb-32px text-center">
          <div class="mb-16px text-6xl text-blue-400">�</div>
          <div class="text-2xl text-white/90">{{ props.title }} - 质量指标详情</div>
        </div>

        <!-- 全屏图表 - 2x2 网格布局 -->
        <div class="grid grid-cols-2 grid-rows-2 h-full gap-24px">
          <!-- 网络质量 -->
          <div class="relative border border-white/20 rounded-12px bg-white/5 p-20px backdrop-blur-sm">
            <div ref="networkQualityRef" class="h-full w-full" />
          </div>

          <!-- 上网质量 -->
          <div class="relative border border-white/20 rounded-12px bg-white/5 p-20px backdrop-blur-sm">
            <div ref="internetQualityRef" class="h-full w-full" />
          </div>

          <!-- 装维服务 -->
          <div class="relative border border-white/20 rounded-12px bg-white/5 p-20px backdrop-blur-sm">
            <div ref="maintenanceServiceRef" class="h-full w-full" />
          </div>

          <!-- 政企产品质量 -->
          <div class="relative border border-white/20 rounded-12px bg-white/5 p-20px backdrop-blur-sm">
            <div ref="enterpriseProductRef" class="h-full w-full" />
          </div>
        </div>
      </div>
    </template>
  </DashboardCard>
</template>
